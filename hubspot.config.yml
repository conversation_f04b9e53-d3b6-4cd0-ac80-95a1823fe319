defaultPortal: serverless-test
portals:
  - name: serverless-test
    portalId: *********
    env: prod
    authType: personalaccesskey
    auth:
      tokenInfo:
        accessToken: >-
          CLCiqIX0MhIaAA0AQAAA4G0DQAJYAgEAAPgAAAAAAAAAAAYYj_3mcyDXgs0lKO7pDDIUJpJQiJH_CnOzWjklbLAz7YV-5Nk6TwAABEEAAAB4AAAAAAAAAAAAgAAAAAAABAAAwAcAjvAxPGBQBgBEAGBA_BgGABAgAwAAAAAAPP4DAAAAAAAAAAwEAAAAAAAAAAAAABAAAMBCFOrMqOB19Gp5DqgcTo5s3bFu-aBTSgNuYTJSAFoAYAFo14LNJXAA
        expiresAt: '2025-06-05T15:16:16.850Z'
    accountType: DEVELOPER_TEST
    personalAccessKey: >-
      CiRuYTItZjA0YS0yNDBkLTQ4MTItYTZkZC01OTUxODI3ZWIwNTUQj_3mcxjXgs0lKhkABeaRgno71Q6TGKGUtEDt-KBKlOZo5-iLSgNuYTI
    parentAccountId: *********
