defaultPortal: serverless-test
portals:
  - name: serverless-test
    portalId: *********
    env: prod
    authType: personalaccesskey
    auth:
      tokenInfo:
        accessToken: >-
          CLHjuJz0MhIaAA0AQAAA4G0DQAJYAgEAAPgAAAAAAAAAAAYYj_3mcyDXgs0lKO7pDDIUVZAWSvi_lBQKwDyGvi-ejTbV4fE6TwAABEEAAAB4AAAAAAAAAAAAgAAAAAAABAAAwAcAjvAxPGBQBgBEAGBA_BgGABAgAwAAAAAAPP4DAAAAAAAAAAwEAAAAAAAAAAAAABAAAMBCFMJA_VSs_B4-9rGNG6OMvRnw-Z8KSgNuYTJSAFoAYAFo14LNJXAA
        expiresAt: '2025-06-06T04:44:41.822Z'
    accountType: DEVELOPER_TEST
    personalAccessKey: >-
      Ci<PERSON>uY<PERSON>tZjA0YS0yNDBkLTQ4MTItYTZkZC01OTUxODI3ZWIwNTUQj_3mcxjXgs0lKhkABeaRgno71Q6TGKGUtEDt-KBKlOZo5-iLSgNuYTI
    parentAccountId: *********
