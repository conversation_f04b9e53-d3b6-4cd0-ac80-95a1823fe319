{"name": "Serverless function app", "description": "This app runs a serverless function to fetch a quote using the Zen Quotes API.", "scopes": ["crm.schemas.companies.write", "crm.schemas.contacts.write", "crm.schemas.deals.read", "crm.schemas.deals.write", "o<PERSON>h", "crm.objects.owners.read", "crm.objects.users.read", "crm.objects.contacts.write", "crm.objects.users.write", "crm.objects.companies.write", "crm.objects.companies.read", "crm.objects.deals.read", "crm.schemas.contacts.read", "crm.objects.deals.write", "crm.objects.contacts.read", "crm.schemas.companies.read"], "uid": "serverless-function-app", "public": false, "extensions": {"crm": {"cards": [{"file": "extensions/example-card.json"}]}}}